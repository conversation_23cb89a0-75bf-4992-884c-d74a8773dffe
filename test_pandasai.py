# 测试pandasai的使用

import os
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import AzureOpenAI
from dotenv import load_dotenv

load_dotenv(override=True)

llm = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_base=os.getenv("AZURE_OPENAI_ENDPOINT"),
    api_version=os.getenv("AZURE_API_VERSION"),
    deployment_name="gpt-4.1-mini",
)

csv_file = "/Volumes/PSSD/code_files/mcp-cms/订舱明细数据_bookings_2024-05-01_2024-05-31_4250条记录_20250708_204425.csv"

csv_data = pd.read_csv(csv_file)

sdf = SmartDataframe(
    csv_data,
    config={
        "llm": llm,
        "enable_cache": False,
    },
)

respones = sdf.chat("分析客户“上鸿”(客户名称的一部分），以一周为一个周期，这个月的各个周期的票数、rt、收入、利润的变动分析（文字分析）。")
print(respones)




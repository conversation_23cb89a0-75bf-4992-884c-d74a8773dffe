#!/usr/bin/env python3
"""
全局数据缓存管理器
支持跨端点的数据缓存共享，优化重复数据查询性能
"""

import os
import time
import threading
import functools
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from .logger_config import setup_logger

# 配置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)


class GlobalDataCacheManager:
    """全局数据缓存管理器 - 支持跨端点缓存共享"""
    
    def __init__(self, default_expire_minutes: int = 30, max_cache_size: int = 100):
        self.cache = {}  # 缓存数据存储
        self.cache_timestamps = {}  # 缓存时间戳
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'total_requests': 0,
            'cross_endpoint_hits': 0  # 跨端点缓存命中次数
        }
        self.default_expire_minutes = default_expire_minutes
        self.max_cache_size = max_cache_size
        self.lock = threading.Lock()
        
        logger.info(f"全局数据缓存管理器初始化: 过期时间={default_expire_minutes}分钟, 最大缓存={max_cache_size}条")
    
    def _generate_cache_key(self, func_name: str, *args, **kwargs) -> str:
        """生成缓存键"""
        # 对于数据获取函数，主要基于日期范围生成键
        if len(args) >= 2:
            begin_date, end_date = args[0], args[1]
            return f"{func_name}_{begin_date}_{end_date}"
        else:
            # 通用键生成
            args_str = "_".join(str(arg) for arg in args)
            kwargs_str = "_".join(f"{k}={v}" for k, v in sorted(kwargs.items()))
            return f"{func_name}_{args_str}_{kwargs_str}"
    
    def _is_expired(self, timestamp: float) -> bool:
        """检查缓存是否过期"""
        expire_time = self.default_expire_minutes * 60  # 转换为秒
        return time.time() - timestamp > expire_time
    
    def _cleanup_expired(self):
        """清理过期缓存"""
        expired_keys = []
        
        for key, timestamp in self.cache_timestamps.items():
            if self._is_expired(timestamp):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
            del self.cache_timestamps[key]
            logger.debug(f"清理过期缓存: {key}")
        
        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存条目")
    
    def _enforce_size_limit(self):
        """强制执行缓存大小限制"""
        if len(self.cache) > self.max_cache_size:
            # 删除最旧的缓存条目
            sorted_items = sorted(self.cache_timestamps.items(), key=lambda x: x[1])
            items_to_remove = len(self.cache) - self.max_cache_size + 1
            
            for i in range(items_to_remove):
                key_to_remove = sorted_items[i][0]
                del self.cache[key_to_remove]
                del self.cache_timestamps[key_to_remove]
                logger.debug(f"删除最旧缓存: {key_to_remove}")
            
            logger.info(f"强制清理了 {items_to_remove} 个缓存条目以维持大小限制")
    
    def get(self, func_name: str, *args, **kwargs) -> Optional[Any]:
        """获取缓存数据"""
        with self.lock:
            self.cache_stats['total_requests'] += 1
            
            # 清理过期缓存
            self._cleanup_expired()
            
            cache_key = self._generate_cache_key(func_name, *args, **kwargs)
            
            if cache_key in self.cache and not self._is_expired(self.cache_timestamps[cache_key]):
                self.cache_stats['hits'] += 1
                logger.info(f"缓存命中: {cache_key}")
                return self.cache[cache_key]
            else:
                self.cache_stats['misses'] += 1
                logger.info(f"缓存未命中: {cache_key}")
                return None
    
    def set(self, func_name: str, data: Any, *args, **kwargs):
        """设置缓存数据"""
        with self.lock:
            cache_key = self._generate_cache_key(func_name, *args, **kwargs)
            
            # 强制执行大小限制
            self._enforce_size_limit()
            
            self.cache[cache_key] = data
            self.cache_timestamps[cache_key] = time.time()
            
            # 记录数据量信息
            record_count = 0
            if isinstance(data, dict) and 'total_count' in data:
                record_count = data['total_count']
            elif isinstance(data, list):
                record_count = len(data)
            
            logger.info(f"缓存数据: {cache_key}, 记录数: {record_count}")
    
    def clear(self):
        """清空所有缓存"""
        with self.lock:
            cleared_count = len(self.cache)
            self.cache.clear()
            self.cache_timestamps.clear()
            logger.info(f"清空了所有缓存: {cleared_count} 个条目")
    
    def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        with self.lock:
            hit_rate = (self.cache_stats['hits'] / max(self.cache_stats['total_requests'], 1)) * 100
            return {
                'cache_size': len(self.cache),
                'max_cache_size': self.max_cache_size,
                'hit_rate': round(hit_rate, 2),
                'total_hits': self.cache_stats['hits'],
                'total_misses': self.cache_stats['misses'],
                'total_requests': self.cache_stats['total_requests'],
                'cross_endpoint_hits': self.cache_stats['cross_endpoint_hits'],
                'expire_minutes': self.default_expire_minutes
            }


# 全局缓存管理器实例
global_cache_manager = GlobalDataCacheManager(
    default_expire_minutes=int(os.getenv('DATA_CACHE_EXPIRE_MINUTES', '30')),
    max_cache_size=int(os.getenv('DATA_CACHE_MAX_SIZE', '100'))
)


def cached_data_function(func: Callable) -> Callable:
    """
    数据函数缓存装饰器
    自动缓存函数结果，支持跨端点共享
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 检查缓存
        cached_result = global_cache_manager.get(func.__name__, *args, **kwargs)
        if cached_result is not None:
            return cached_result
        
        # 缓存未命中，执行函数
        logger.info(f"执行函数 {func.__name__} 获取数据")
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        # 缓存结果（如果有数据）
        if result:
            global_cache_manager.set(func.__name__, result, *args, **kwargs)
            logger.info(f"函数 {func.__name__} 执行完成，耗时: {execution_time:.2f} 秒")
        
        return result
    
    return wrapper


def async_cached_data_function(func: Callable) -> Callable:
    """
    异步数据函数缓存装饰器
    自动缓存异步函数结果，支持跨端点共享
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # 检查缓存
        cached_result = global_cache_manager.get(func.__name__, *args, **kwargs)
        if cached_result is not None:
            return cached_result
        
        # 缓存未命中，执行函数
        logger.info(f"执行异步函数 {func.__name__} 获取数据")
        start_time = time.time()
        result = await func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        # 缓存结果（如果有数据）
        if result:
            global_cache_manager.set(func.__name__, result, *args, **kwargs)
            logger.info(f"异步函数 {func.__name__} 执行完成，耗时: {execution_time:.2f} 秒")
        
        return result
    
    return wrapper

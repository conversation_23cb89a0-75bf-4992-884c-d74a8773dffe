# CSV数据分析工具使用说明

## 📋 概述

本工具包提供了两种CSV数据分析方案：

1. **简化版分析器** (`simple_csv_analyzer.py`) - 基于pandas的传统数据分析
2. **PandasAI分析器** (`pandasai_csv_analyzer.py`) - 基于PandasAI的AI驱动数据分析

## 🚀 快速开始

### 1. 简化版分析器

不需要API密钥，基于pandas提供全面的数据分析功能。

```bash
# 生成完整报告
python utils/basic/simple_csv_analyzer.py -f your_data.csv -r

# 生成数据洞察
python utils/basic/simple_csv_analyzer.py -f your_data.csv -i

# 导出筛选数据
python utils/basic/simple_csv_analyzer.py -f your_data.csv -e "pro2_profit > 10000" -o high_profit.csv
```

### 2. PandasAI分析器

需要OpenAI API密钥，支持自然语言查询。

```bash
# 设置API密钥
export OPENAI_API_KEY=your_api_key

# 交互式模式
python utils/basic/pandasai_csv_analyzer.py -f your_data.csv -i

# 直接问问题
python utils/basic/pandasai_csv_analyzer.py -f your_data.csv -q "哪个客户的订单最多？"

# 生成数据洞察
python utils/basic/pandasai_csv_analyzer.py -f your_data.csv --insights
```

## 📊 功能特性

### 简化版分析器功能

- **数据质量分析**
  - 重复行检测
  - 空值统计
  - 数据类型分析

- **业务指标分析**
  - 收入/成本/利润分析
  - 销售员绩效统计
  - 客户订单分析
  - 航线热度分析

- **数据导出**
  - 支持Excel/CSV格式
  - 条件筛选导出
  - 数据验证

### PandasAI分析器功能

- **自然语言查询**
  - 支持中文问题
  - 智能数据洞察
  - 上下文理解

- **AI数据分析**
  - 趋势识别
  - 异常检测
  - 相关性分析

- **多模型支持**
  - OpenAI GPT系列
  - Azure OpenAI
  - 其他兼容模型

## 🛠️ 针对MCP CMS数据的优化

两个分析器都针对MCP CMS系统的数据结构进行了优化：

### 识别的业务字段

- **收入字段**: `revenue`, `total_revenue`, `pro2_total_revenue`
- **成本字段**: `cost`, `total_cost`, `pro2_total_cost`
- **利润字段**: `profit`, `pro2_profit`
- **销售员字段**: `sales_person`, `pro2_sales_person`
- **客户字段**: `customer`, `customer_name`, `pro2_customer_name`
- **日期字段**: `booking_date`, `pro2_booking_date`
- **港口字段**: `pol`, `pro2_pol_name`, `pod`, `pro2_pod_name`

### 业务分析指标

- **财务指标**: 总收入、平均利润、利润率
- **销售指标**: 销售员排名、客户排名
- **运营指标**: 热门航线、订单分布
- **质量指标**: 数据完整性、异常值检测

## 📝 使用示例

### 基本数据分析

```python
from utils.basic.simple_csv_analyzer import SimpleCSVAnalyzer

# 创建分析器
analyzer = SimpleCSVAnalyzer()

# 加载数据
analyzer.load_csv_data("booking_data.csv")

# 生成报告
report = analyzer.generate_report()
print(report)
```

### AI驱动分析

```python
from utils.basic.pandasai_csv_analyzer import PandasAICSVAnalyzer

# 创建分析器
analyzer = PandasAICSVAnalyzer(api_key="your_api_key")

# 加载数据
analyzer.load_csv_data("booking_data.csv")

# 自然语言查询
answer = analyzer.chat_with_data("哪个月的收入最高？")
print(answer)
```

## 🔧 配置选项

### 环境变量

```bash
# PandasAI配置
OPENAI_API_KEY=your_openai_api_key
AZURE_OPENAI_ENDPOINT=your_azure_endpoint
AZURE_OPENAI_API_VERSION=2023-05-15
```

### 命令行参数

**简化版分析器**:
- `-f, --file`: CSV文件路径 (必须)
- `-r, --report`: 生成完整报告
- `-i, --insights`: 生成数据洞察
- `-e, --export`: 导出筛选条件
- `-o, --output`: 输出文件路径

**PandasAI分析器**:
- `-f, --file`: CSV文件路径 (必须)
- `-k, --api-key`: OpenAI API密钥
- `-m, --model`: 模型类型 (openai, azure)
- `-i, --interactive`: 交互式模式
- `-q, --question`: 直接问问题
- `--insights`: 生成数据洞察
- `-e, --export`: 导出筛选条件
- `-o, --output`: 输出文件路径

## 🧪 测试

运行测试脚本验证功能：

```bash
python test_csv_analyzer.py
```

## 📋 系统要求

- Python 3.12+
- 依赖包：pandas, numpy, openpyxl, pandasai

## 🚨 注意事项

1. **API密钥安全**: 不要在代码中硬编码API密钥
2. **数据隐私**: 使用PandasAI时，数据会发送到第三方服务
3. **文件编码**: 工具自动检测常见编码格式
4. **内存使用**: 大文件可能消耗较多内存

## 🔍 故障排除

### 常见问题

1. **导入错误**
   ```bash
   # 安装依赖
   uv add pandasai pyyaml
   ```

2. **编码错误**
   - 工具会自动尝试多种编码
   - 支持 UTF-8、GBK、GB2312、Latin-1

3. **API限制**
   - 检查API密钥是否有效
   - 注意API请求频率限制

### 性能优化

1. **大文件处理**
   - 使用分块读取
   - 考虑数据采样

2. **内存优化**
   - 及时释放不需要的数据
   - 使用适当的数据类型

## 📖 扩展开发

### 添加新的分析功能

1. 在 `SimpleCSVAnalyzer` 类中添加新方法
2. 更新 `generate_report()` 方法
3. 添加相应的命令行参数

### 支持新的数据格式

1. 扩展 `load_csv_data()` 方法
2. 添加格式检测逻辑
3. 更新错误处理机制

## 📧 技术支持

如有问题，请查看日志文件或联系系统管理员。
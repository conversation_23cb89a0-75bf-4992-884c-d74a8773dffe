#!/usr/bin/env python3
"""
CSV数据分析示例脚本
演示如何使用PandasAI分析器对MCP CMS系统导出的CSV数据进行分析
"""

import os
import sys
import asyncio
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from utils.basic.pandasai_csv_analyzer import PandasAICSVAnalyzer
from utils.basic.simple_excel_export import export_to_excel_with_oss
from utils.basic.data_conn_unified import get_database_connection
from utils.basic.logger_config import setup_logger

# 设置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

async def generate_sample_data():
    """生成示例数据用于分析"""
    try:
        # 从数据库获取最近的booking数据作为样本
        db_conn = await get_database_connection()
        if not db_conn:
            logger.error("数据库连接失败")
            return None
        
        # 获取最近100条booking记录
        query = """
        SELECT TOP 100
            pro2_booking_id,
            pro2_reference_no,
            pro2_customer_name,
            pro2_shipper_name,
            pro2_consignee_name,
            pro2_pol_name,
            pro2_pod_name,
            pro2_etd,
            pro2_eta,
            pro2_booking_date,
            pro2_cargo_type,
            pro2_total_revenue,
            pro2_total_cost,
            pro2_profit,
            pro2_sales_person
        FROM v_pro2_booking_detail
        WHERE pro2_booking_date >= ?
        ORDER BY pro2_booking_date DESC
        """
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        params = [start_date.strftime('%Y-%m-%d')]
        
        cursor = db_conn.cursor()
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        if not rows:
            logger.warning("未找到样本数据")
            return None
        
        # 创建CSV文件
        output_path = "/tmp/sample_booking_data.csv"
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 写入CSV文件
        import csv
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(columns)
            writer.writerows(rows)
        
        cursor.close()
        db_conn.close()
        
        logger.info(f"样本数据生成成功: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"生成样本数据失败: {e}")
        return None

def analyze_booking_data():
    """分析booking数据的示例"""
    logger.info("开始分析booking数据...")
    
    # 创建分析器
    analyzer = PandasAICSVAnalyzer(
        api_key=os.getenv("OPENAI_API_KEY"),
        model_type="openai"
    )
    
    # 使用示例CSV文件（如果存在）
    sample_file = "/tmp/sample_booking_data.csv"
    
    if not os.path.exists(sample_file):
        logger.error(f"样本文件不存在: {sample_file}")
        logger.info("请先运行: python csv_analysis_example.py --generate-sample")
        return
    
    # 加载数据
    if not analyzer.load_csv_data(sample_file):
        logger.error("数据加载失败")
        return
    
    # 获取数据摘要
    summary = analyzer.get_data_summary()
    print("\n=== 数据摘要 ===")
    print(f"数据形状: {summary.get('shape', 'N/A')}")
    print(f"列数: {len(summary.get('columns', []))}")
    print(f"内存使用: {summary.get('memory_usage', 0) / 1024 / 1024:.2f} MB")
    
    # 业务相关的问题示例
    business_questions = [
        "哪个销售人员的总收入最高？",
        "过去30天的平均利润率是多少？",
        "哪个航线(POL到POD)的预订量最多？",
        "收入最高的前5个客户是谁？",
        "哪种货物类型的利润最高？",
        "ETD和ETA之间的平均航行时间是多少？",
        "是否有负利润的订单？如果有，占总订单的比例是多少？"
    ]
    
    print("\n=== 业务数据分析 ===")
    for i, question in enumerate(business_questions, 1):
        print(f"\n{i}. 问题: {question}")
        try:
            answer = analyzer.chat_with_data(question)
            print(f"   回答: {answer}")
        except Exception as e:
            print(f"   回答: 分析失败 - {str(e)}")
    
    # 数据洞察
    print("\n=== 自动数据洞察 ===")
    insights = analyzer.generate_insights()
    for i, insight in enumerate(insights, 1):
        print(f"\n{i}. {insight}")
    
    # 导出高利润订单
    print("\n=== 数据导出示例 ===")
    try:
        high_profit_file = "/tmp/high_profit_bookings.csv"
        success = analyzer.export_filtered_data(
            "pro2_profit > 10000",  # 利润大于10000的订单
            high_profit_file
        )
        if success:
            print(f"高利润订单导出成功: {high_profit_file}")
        else:
            print("高利润订单导出失败")
    except Exception as e:
        print(f"数据导出失败: {e}")

def interactive_analysis():
    """交互式分析模式"""
    logger.info("启动交互式分析模式...")
    
    analyzer = PandasAICSVAnalyzer(
        api_key=os.getenv("OPENAI_API_KEY"),
        model_type="openai"
    )
    
    # 让用户选择CSV文件
    print("请输入CSV文件路径:")
    file_path = input().strip()
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    # 加载数据
    if not analyzer.load_csv_data(file_path):
        print("数据加载失败")
        return
    
    # 显示数据摘要
    summary = analyzer.get_data_summary()
    print(f"\n数据加载成功!")
    print(f"数据形状: {summary.get('shape', 'N/A')}")
    print(f"列名: {', '.join(summary.get('columns', []))}")
    
    # 交互式问答
    print("\n=== 交互式数据分析 ===")
    print("您可以用自然语言问问题，例如:")
    print("- 数据中有多少行？")
    print("- 哪一列的数据最多？")
    print("- 显示前10行数据")
    print("- 计算某列的平均值")
    print("输入 'exit' 或 'quit' 退出\n")
    
    while True:
        try:
            question = input("请输入您的问题: ").strip()
            if question.lower() in ['exit', 'quit', '退出']:
                break
            if not question:
                continue
            
            answer = analyzer.chat_with_data(question)
            print(f"回答: {answer}\n")
            
        except KeyboardInterrupt:
            print("\n退出程序")
            break
        except Exception as e:
            print(f"错误: {e}\n")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CSV数据分析示例')
    parser.add_argument('--generate-sample', action='store_true', help='生成样本数据')
    parser.add_argument('--analyze', action='store_true', help='分析booking数据')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')
    
    args = parser.parse_args()
    
    if args.generate_sample:
        asyncio.run(generate_sample_data())
    elif args.analyze:
        analyze_booking_data()
    elif args.interactive:
        interactive_analysis()
    else:
        print("请选择一个操作:")
        print("  --generate-sample  生成样本数据")
        print("  --analyze         分析booking数据")
        print("  --interactive     交互式模式")

if __name__ == "__main__":
    main()
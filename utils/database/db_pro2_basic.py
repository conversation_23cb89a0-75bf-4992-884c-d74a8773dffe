# Pro2数据库基本操作

import asyncio
from typing import Dict, List, Any
from pydantic import BaseModel
from dotenv import load_dotenv

from utils.basic.data_conn_unified import execute_pro2_query_async
from utils.basic.db_pro2_sea_air_profit import (
    get_booking_details_with_transhipment,
    query_job_details_with_statistics_by_date,
    get_company_list,
    get_user_info
)
from utils.basic.logger_config import setup_logger
from utils.basic.data_cache_manager import async_cached_data_function

load_dotenv(override=True)

# 配置日志
logger = setup_logger(
    name=__name__,
    level="warning",
    log_to_console=True,
    log_to_file=True
)

class Company(BaseModel):
    code: str
    name: str
    address: str

# 根据公司名称的一部分搜索公司信息
async def search_company_by_part_name(name_part: str) -> List[Company]:
    """
    根据公司名称的一部分搜索公司信息
    """
    results = await asyncio.to_thread(get_company_list, name_part)
    return results

# 根据full_name/username获得user info(包括user_id, username, full_name, dept_name)
async def search_user_by_part_name(name_part: str) -> List[Dict[str, Any]]:
    """
    根据full_name/username获得user info
    """
    results = await asyncio.to_thread(get_user_info, name_part)
    return results

# 根据部门名称的一部分搜索部门信息
async def search_department_by_part_name(name_part: str) -> List[Dict[str, Any]]:
    """
    根据部门名称的一部分搜索部门信息
    
    Args:
        name_part: 部门名称的一部分
        
    Returns:
        匹配的部门列表，包含dept_id, name, description等字段
    """
    if not name_part:
        return []
    
    # 使用模糊匹配查询部门信息
    sql = """
        SELECT DEPT_ID as dept_id, 
               NAME as name, 
               DESCRIPTION as description,
               IS_SALES as is_sales,
               TEL as tel,
               FAX as fax,
               EMAIL as email
        FROM USERS_DEPARTMENT 
        WHERE UPPER(NAME) LIKE UPPER(?)
        ORDER BY NAME
    """
    
    try:
        search_pattern = f'%{name_part}%'
        # 尝试多种字符集，优先使用UTF-8，但保留GBK作为备选
        results = None
        for charset in ['UTF-8', 'GBK', 'WIN1252', 'ISO8859_1']:
            try:
                logger.debug(f"尝试使用字符集 {charset} 搜索部门: {name_part}")
                results = await execute_pro2_query_async(sql, (search_pattern,), fetch_all=True, charset=charset)
                logger.debug(f"使用 {charset} 字符集成功查询到 {len(results) if results else 0} 条结果")
                break
            except Exception as e:
                logger.debug(f"字符集 {charset} 查询失败: {e}")
                if "can't encode" in str(e) or "codec" in str(e):
                    continue
                else:
                    # 非编码相关的错误，直接抛出
                    raise
        
        if results is None:
            logger.error(f"所有字符集都无法执行查询: {name_part}")
            return []
        
        if results:
            # 转换为字典列表
            dept_list = []
            for row in results:
                dept_dict = {
                    'dept_id': row[0],
                    'name': row[1],
                    'description': row[2] if row[2] else '',
                    'is_sales': bool(row[3]) if row[3] is not None else False,
                    'tel': row[4] if row[4] else '',
                    'fax': row[5] if row[5] else '',
                    'email': row[6] if row[6] else ''
                }
                dept_list.append(dept_dict)
            
            logger.info(f"部门搜索 '{name_part}' 找到 {len(dept_list)} 个匹配结果")
            return dept_list
        else:
            logger.warning(f"部门搜索 '{name_part}' 未找到匹配结果")
            return []
            
    except Exception as e:
        logger.error(f"部门搜索失败: {name_part}, 错误: {str(e)}")
        return []



# 根据时间周期查询海运空运损益并添加转运利润字段 (调度器使用)
@async_cached_data_function
async def get_sea_air_profit_with_transhipment(begin_date: str, end_date: str) -> Dict[str, Any]:
    """
    根据时间周期查询海运空运损益并添加转运利润字段 (调度器使用)
    使用 query_business_details_by_date 函数直接获取包含转运功能的业务明细数据
    """
    logger.warning(f"开始查询海空损益数据（含转运）: {begin_date} 到 {end_date}")
    
    try:
        # 使用已存在的函数直接获取包含转运功能的业务数据
        results = await asyncio.to_thread(
            get_booking_details_with_transhipment,
            begin_date,
            end_date,
            logger_prefix="[Scheduler]"
        )
        
        logger.warning(f"海空损益数据查询完成（含转运）: {len(results)} 条记录")
        
        return {
            'data': results,
            'total_count': len(results),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）'
            }
        }
        
    except Exception as e:
        logger.error(f"查询海空损益数据失败（含转运）: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）',
                'error': f'查询失败: {str(e)}'
            }
        }

# 根据时间周期查询作业明细并添加转运利润字段 (调度器使用)
@async_cached_data_function
async def get_job_details_with_transhipment(begin_date: str, end_date: str) -> Dict[str, Any]:
    """
    根据时间周期查询作业明细并添加转运利润字段 (调度器使用)
    使用 query_job_details_with_statistics_by_date 函数直接获取包含转运功能的作业明细数据
    """
    logger.warning(f"开始查询作业明细数据（含转运）: {begin_date} 到 {end_date}")
    
    try:
        # 使用已存在的函数直接获取包含转运功能的作业数据
        results = await asyncio.to_thread(
            query_job_details_with_statistics_by_date,
            begin_date,
            end_date,
            logger_prefix="[Scheduler]"
        )
        
        if not results:
            logger.warning(f"未查询到作业明细数据: {begin_date} 到 {end_date}")
            return {
                'data': [],
                'total_count': 0,
                'query_info': {
                    'date_range': f'{begin_date} 到 {end_date}',
                    'data_type': '全部作业明细数据（含转运）',
                    'message': '未查询到数据'
                }
            }
        
        logger.warning(f"作业明细数据查询完成（含转运）: {len(results)} 条记录")
        
        return {
            'data': results,
            'total_count': len(results),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）'
            }
        }
        
    except Exception as e:
        logger.error(f"查询作业明细数据失败（含转运）: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）',
                'error': f'查询失败: {str(e)}'
            }
        }

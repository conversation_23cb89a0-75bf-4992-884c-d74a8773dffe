#!/usr/bin/env python3
"""
测试 PandasAI 集成功能
"""

import os
import asyncio
import requests
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv(override=True)

# 测试配置
BASE_URL = "http://127.0.0.1:8011"
MCP_TOKEN = os.getenv("MCP_TOKEN")

def test_data_analysis():
    """测试数据分析功能"""

    # 准备测试数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

    # 测试请求数据
    test_requests = [
        {
            "begin_date": start_date,
            "end_date": end_date,
            "data_type": "booking",
            "question": "分析最近一个月的订舱数据，包括总收入、总成本、利润率，以及主要的业务类型分布",
            "language": "zh"
        },
        {
            "begin_date": start_date,
            "end_date": end_date,
            "data_type": "booking",
            "question": "找出利润率最高的前5个客户",
            "language": "zh"
        },
        {
            "begin_date": start_date,
            "end_date": end_date,
            "data_type": "job",
            "question": "分析工作档数据的操作完成情况和审核状态分布",
            "language": "zh"
        }
    ]
    
    headers = {
        "Authorization": f"Bearer {MCP_TOKEN}",
        "Content-Type": "application/json"
    }
    
    print(f"🧪 开始测试 PandasAI 数据分析功能")
    print(f"📅 测试日期范围: {start_date} 至 {end_date}")
    print(f"🔗 服务地址: {BASE_URL}")
    print("-" * 60)
    
    for i, test_data in enumerate(test_requests, 1):
        print(f"\n🔍 测试 {i}: {test_data['data_type']} 数据分析")
        print(f"❓ 问题: {test_data['question']}")
        
        try:
            # 发送请求
            response = requests.post(
                f"{BASE_URL}/analyze/data",
                headers=headers,
                json=test_data,
                timeout=300  # 5分钟超时
            )
            
            if response.status_code == 200:
                result = response.json()
                performance_info = result.get('performance_info', {})
                cache_used = performance_info.get('cache_used', False)
                cache_stats = performance_info.get('cache_stats', {})

                print(f"✅ 分析成功")
                print(f"📊 数据记录数: {result.get('data_info', {}).get('record_count', 0)}")
                print(f"⏱️ 总执行时间: {performance_info.get('total_execution_time_seconds', 0):.2f} 秒")
                print(f"🗄️ 数据获取时间: {performance_info.get('data_fetch_time_seconds', 0):.2f} 秒")
                print(f"💾 缓存使用: {'是' if cache_used else '否'}")
                print(f"📈 缓存命中率: {cache_stats.get('hit_rate', 0):.1f}%")
                print(f"🤖 AI分析结果:")
                print("-" * 40)
                print(result.get('analysis_result', '无结果'))
                print("-" * 40)
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ 请求超时")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    print(f"\n🏁 测试完成")


def test_cache_stats():
    """测试缓存统计功能"""
    headers = {
        "Authorization": f"Bearer {MCP_TOKEN}",
        "Content-Type": "application/json"
    }

    print(f"\n📊 测试缓存统计功能")

    try:
        response = requests.get(f"{BASE_URL}/cache/stats", headers=headers, timeout=10)
        if response.status_code == 200:
            result = response.json()
            cache_stats = result.get('cache_stats', {})
            print(f"✅ 缓存统计获取成功")
            print(f"💾 当前缓存大小: {cache_stats.get('cache_size', 0)}/{cache_stats.get('max_cache_size', 0)}")
            print(f"📈 缓存命中率: {cache_stats.get('hit_rate', 0):.1f}%")
            print(f"🎯 总命中次数: {cache_stats.get('total_hits', 0)}")
            print(f"❌ 总未命中次数: {cache_stats.get('total_misses', 0)}")
            print(f"📊 总请求次数: {cache_stats.get('total_requests', 0)}")
            print(f"⏰ 缓存过期时间: {cache_stats.get('expire_minutes', 0)} 分钟")
            return True
        else:
            print(f"❌ 缓存统计获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 缓存统计测试异常: {e}")
        return False


def test_health_check():
    """测试健康检查"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务健康状态: {result.get('status')}")
            print(f"📊 数据库连接: {'正常' if result.get('database_connection') else '异常'}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False


def main():
    """主函数"""
    print("🚀 PandasAI 集成功能测试")
    print("=" * 60)
    
    # 检查环境变量
    if not MCP_TOKEN:
        print("❌ 未找到 MCP_TOKEN 环境变量")
        return
    
    # 健康检查
    print("🔍 执行健康检查...")
    if not test_health_check():
        print("❌ 服务不可用，退出测试")
        return
    
    print("\n" + "=" * 60)

    # 测试数据分析功能（包含缓存测试）
    test_data_analysis()

    print("\n" + "=" * 60)

    # 测试缓存统计功能
    test_cache_stats()


if __name__ == "__main__":
    main()
